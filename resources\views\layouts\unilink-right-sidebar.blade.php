<!-- Right Sidebar -->
<aside class="hidden lg:block fixed right-0 top-16 w-80 xl:w-96 h-[calc(100vh-4rem)] bg-custom-lightest shadow-lg border-l border-custom-second-darkest overflow-y-auto custom-scrollbar z-30">
    <div class="p-4 space-y-4">
        <!-- Suggested Organizations -->
        <div class="bg-white rounded-lg p-4 border border-custom-second-darkest border-opacity-20">
            <h3 class="text-sm font-semibold text-custom-darkest mb-3">Suggested for you</h3>
            <div class="space-y-2">
                <!-- Sam<PERSON> suggested org -->
                <div class="flex items-center space-x-3 p-2 rounded-lg hover:bg-custom-lightest transition-colors">
                    <img class="h-8 w-8 rounded-full bg-custom-green bg-opacity-20" src="https://ui-avatars.com/api/?name=Student+Council&color=7BC74D&background=EEEEEE" alt="Student Council">
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-custom-darkest truncate">Student Council</p>
                        <p class="text-xs text-custom-second-darkest">245 members</p>
                    </div>
                    <button class="text-xs bg-custom-green text-custom-darkest px-2 py-1 rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest transition-colors">
                        Join
                    </button>
                </div>

                <div class="flex items-center space-x-3 p-2 rounded-lg hover:bg-custom-lightest transition-colors">
                    <img class="h-8 w-8 rounded-full bg-custom-green bg-opacity-20" src="https://ui-avatars.com/api/?name=Computer+Society&color=7BC74D&background=EEEEEE" alt="Computer Society">
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-custom-darkest truncate">Computer Society</p>
                        <p class="text-xs text-custom-second-darkest">189 members</p>
                    </div>
                    <button class="text-xs bg-custom-green text-custom-darkest px-2 py-1 rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest transition-colors">
                        Join
                    </button>
                </div>

                <div class="flex items-center space-x-3 p-2 rounded-lg hover:bg-custom-lightest transition-colors">
                    <img class="h-8 w-8 rounded-full bg-custom-green bg-opacity-20" src="https://ui-avatars.com/api/?name=Drama+Club&color=7BC74D&background=EEEEEE" alt="Drama Club">
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-custom-darkest truncate">Drama Club</p>
                        <p class="text-xs text-custom-second-darkest">67 members</p>
                    </div>
                    <button class="text-xs bg-custom-green text-custom-darkest px-2 py-1 rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest transition-colors">
                        Join
                    </button>
                </div>
            </div>

            <a href="{{ route('organizations.index') }}" class="block mt-4 text-sm text-custom-green hover:text-custom-second-darkest font-medium">
                View all organizations →
            </a>
        </div>

        <!-- Contacts -->
        <div class="bg-white rounded-lg p-4 border border-custom-second-darkest border-opacity-20">
            <h3 class="text-sm font-semibold text-custom-darkest mb-3">Contacts</h3>
            <div class="space-y-2">
                <div class="flex items-center space-x-2 p-1 rounded hover:bg-custom-lightest transition-colors">
                    <div class="relative">
                        <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=John+Doe&color=7BC74D&background=EEEEEE" alt="John Doe">
                        <div class="absolute bottom-0 right-0 w-3 h-3 bg-custom-green border-2 border-white rounded-full"></div>
                    </div>
                    <span class="text-sm text-custom-darkest">John Doe</span>
                </div>
                <div class="flex items-center space-x-2 p-1 rounded hover:bg-custom-lightest transition-colors">
                    <div class="relative">
                        <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=Jane+Smith&color=7BC74D&background=EEEEEE" alt="Jane Smith">
                        <div class="absolute bottom-0 right-0 w-3 h-3 bg-custom-green border-2 border-white rounded-full"></div>
                    </div>
                    <span class="text-sm text-custom-darkest">Jane Smith</span>
                </div>
                <div class="flex items-center space-x-2 p-1 rounded hover:bg-custom-lightest transition-colors">
                    <div class="relative">
                        <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=Mike+Johnson&color=7BC74D&background=EEEEEE" alt="Mike Johnson">
                        <div class="absolute bottom-0 right-0 w-3 h-3 bg-custom-second-darkest border-2 border-white rounded-full"></div>
                    </div>
                    <span class="text-sm text-custom-darkest">Mike Johnson</span>
                </div>
            </div>
        </div>

        <!-- Sponsored -->
        <div class="bg-white rounded-lg p-4 border border-custom-second-darkest border-opacity-20">
            <h3 class="text-sm font-semibold text-custom-darkest mb-3">Sponsored</h3>
            <div class="space-y-3">
                <div class="flex items-start space-x-3 p-2 rounded-lg hover:bg-custom-lightest transition-colors">
                    <img class="h-12 w-12 rounded-lg object-cover" src="https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=100&h=100&fit=crop&crop=center" alt="Study Abroad">
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-custom-darkest">Study Abroad Programs</p>
                        <p class="text-xs text-custom-second-darkest">Explore opportunities worldwide</p>
                        <p class="text-xs text-custom-green">studyabroad.edu</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</aside>
