<x-feed-layout>

    <!-- Create Post Card -->
    <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest border-opacity-20 p-4 mb-4">
        <div class="flex items-center space-x-3">
            <img class="h-10 w-10 rounded-full" src="{{ auth()->user()->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}" alt="{{ auth()->user()->name }}">
            <div class="flex-1">
                <button onclick="openCreatePostModal()" class="w-full text-left px-4 py-3 bg-custom-lightest hover:bg-custom-lightest hover:bg-opacity-80 rounded-full text-custom-second-darkest transition-colors">
                    What's on your mind, {{ auth()->user()->name }}?
                </button>
            </div>
        </div>
        <div class="flex items-center justify-between mt-3 pt-3 border-t border-custom-second-darkest border-opacity-20">
            <button onclick="openCreatePostModal()" class="flex items-center space-x-2 px-4 py-2 text-custom-second-darkest hover:bg-custom-lightest rounded-lg transition-colors">
                <svg class="w-5 h-5 text-custom-green" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm font-medium">Photo/Video</span>
            </button>
            <button onclick="openCreatePostModal()" class="flex items-center space-x-2 px-4 py-2 text-custom-second-darkest hover:bg-custom-lightest rounded-lg transition-colors">
                <svg class="w-5 h-5 text-custom-green" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm font-medium">Event</span>
            </button>
            <a href="{{ route('scholarships.create') }}" class="flex items-center space-x-2 px-4 py-2 text-custom-second-darkest hover:bg-custom-lightest rounded-lg transition-colors">
                <svg class="w-5 h-5 text-custom-green" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" />
                </svg>
                <span class="text-sm font-medium">Scholarship</span>
            </a>
        </div>
    </div>

    <!-- Filter Pills -->
    <!-- <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest border-opacity-20 p-3 mb-4">
        <div class="flex flex-wrap gap-2">
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-green rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                All Posts
            </button>
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-lightest rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                Events
            </button>
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-lightest rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                Scholarships
            </button>
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-lightest rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                Announcements
            </button>
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-lightest rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                Organizations
            </button>
        </div>
    </div> -->

    <!-- Filter Buttons -->
    <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest border-opacity-20 p-4 mb-6">
        <!-- Search Box -->
        <div class="mb-4">
            <div class="relative">
                <input type="text"
                       id="post-search"
                       placeholder="Search posts..."
                       class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-custom-green focus:border-custom-green">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </div>
                <button id="search-clear" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 hidden">
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>

        <div class="flex flex-wrap items-center gap-3">
            <!-- Filter Label -->
            <span class="text-sm font-medium text-gray-700">Filter by:</span>

            <!-- Post Type Filters -->
            <div class="flex flex-wrap gap-2">
                <button onclick="filterPosts('all')"
                        class="filter-btn active px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="all">
                    All Posts
                </button>
                <button onclick="filterPosts('general')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="general">
                    General
                </button>
                <button onclick="filterPosts('announcement')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="announcement">
                    📢 Announcements
                </button>
                <button onclick="filterPosts('event')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="event">
                    📅 Events
                </button>
                <button onclick="filterPosts('financial_report')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="financial_report">
                    💰 Financial Reports
                </button>
            </div>

            <!-- Separator -->
            <div class="h-6 w-px bg-gray-300"></div>

            <!-- Organization Filters -->
            <div class="flex flex-wrap gap-2">
                <button onclick="filterPosts('personal')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="personal">
                    👤 Personal
                </button>
                <button onclick="filterPosts('organizations')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="organizations">
                    🏢 Organizations
                </button>
            </div>

            <!-- Separator -->
            <div class="h-6 w-px bg-gray-300"></div>

            <!-- Additional Filters -->
            <div class="flex flex-wrap gap-2">
                <button onclick="filterPosts('with_images')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="with_images">
                    🖼️ With Images
                </button>
                <button onclick="filterPosts('pinned')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="pinned">
                    📌 Pinned
                </button>
            </div>

            <!-- Clear Filters -->
            <button onclick="clearAllFilters()"
                    class="ml-auto px-4 py-1 text-sm text-gray-500 hover:text-gray-700 transition-colors">
                Clear All
            </button>
        </div>

        <!-- Active Filters Display -->
        <div id="active-filters" class="mt-3 hidden">
            <div class="flex items-center gap-2">
                <span class="text-xs text-gray-500">Active filters:</span>
                <div id="active-filters-list" class="flex flex-wrap gap-1"></div>
            </div>
        </div>
    </div>

    <!-- Main Feed -->
    <div class="space-y-6" id="posts-container">
        @forelse($posts as $post)
            <x-post-card :post="$post" />
        @empty
            <!-- Empty State -->
            <div id="empty-state" class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10m0 0V6a2 2 0 00-2-2H9a2 2 0 00-2 2v2m10 0v10a2 2 0 01-2 2H9a2 2 0 01-2-2V8m10 0H7m0 0V6a2 2 0 012-2h10a2 2 0 012 2v2M7 8v10a2 2 0 002 2h10a2 2 0 002-2V8" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No posts yet</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by creating your first post!</p>
                <div class="mt-6">
                    <button onclick="openCreatePostModal()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-custom-green hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Create Post
                    </button>
                </div>
            </div>
        @endforelse

        <!-- Pagination -->
        @if($posts->hasPages())
            <div class="pagination-container flex justify-center py-6">
                {{ $posts->links() }}
            </div>
        @endif
    </div>

    <!-- Create Post Modal -->
    @include('components.create-post-modal')

    <!-- Image Modal -->
    @include('components.image-modal')

    <!-- Filter Functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize filter buttons
            initializeFilterButtons();

            // Initialize search functionality
            initializeSearch();
        });

        // Current active filters
        let activeFilters = new Set(['all']);

        // Initialize filter buttons
        function initializeFilterButtons() {
            // Add active class styling to filter buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                if (btn.dataset.filter === 'all') {
                    btn.classList.add('bg-custom-green', 'text-white', 'border-custom-green');
                } else {
                    btn.classList.add('bg-white', 'text-gray-700', 'border-gray-300', 'hover:bg-gray-50');
                }
            });

            // Apply initial filtering
            applyFilters();
        }

        // Filter posts based on selected filter
        function filterPosts(filterType) {
            const filterBtn = document.querySelector(`.filter-btn[data-filter="${filterType}"]`);

            // Handle filter button states
            if (filterType === 'all') {
                // Clear all other filters when 'All Posts' is selected
                clearAllFilters();
                activeFilters.add('all');
                updateFilterButtonStates();
            } else {
                // Remove 'all' filter when any other filter is selected
                activeFilters.delete('all');

                // Toggle the selected filter
                if (activeFilters.has(filterType)) {
                    activeFilters.delete(filterType);
                    // If no filters are active, revert to 'all'
                    if (activeFilters.size === 0) {
                        activeFilters.add('all');
                    }
                } else {
                    activeFilters.add(filterType);
                }

                updateFilterButtonStates();
            }

            // Apply the filters
            applyFilters();
        }

        // Update filter button visual states
        function updateFilterButtonStates() {
            document.querySelectorAll('.filter-btn').forEach(btn => {
                const filter = btn.dataset.filter;

                // Reset all button styles
                btn.classList.remove('bg-custom-green', 'text-white', 'border-custom-green', 'active');
                btn.classList.add('bg-white', 'text-gray-700', 'border-gray-300');

                // Apply active styles to active filters
                if (activeFilters.has(filter)) {
                    btn.classList.remove('bg-white', 'text-gray-700', 'border-gray-300');
                    btn.classList.add('bg-custom-green', 'text-white', 'border-custom-green', 'active');
                }
            });

            // Update active filters display
            updateActiveFiltersDisplay();
        }

        // Initialize search functionality
        function initializeSearch() {
            const searchInput = document.getElementById('post-search');
            const searchClear = document.getElementById('search-clear');
            let searchTimeout;

            if (searchInput) {
                // Handle search input
                searchInput.addEventListener('input', function() {
                    // Show/hide clear button
                    if (this.value.length > 0) {
                        searchClear.classList.remove('hidden');
                    } else {
                        searchClear.classList.add('hidden');
                    }

                    // Debounce search
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        applyFilters();
                    }, 500);
                });

                // Handle clear button
                searchClear.addEventListener('click', function() {
                    searchInput.value = '';
                    searchClear.classList.add('hidden');
                    applyFilters();
                });

                // Handle Enter key
                searchInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        applyFilters();
                    }
                });
            }
        }

        // Apply filters to posts using AJAX
        function applyFilters() {
            // Show loading state
            const postsContainer = document.getElementById('posts-container');
            postsContainer.classList.add('opacity-50');

            // Build filter parameters
            const params = new URLSearchParams();

            // Add search term if present
            const searchInput = document.getElementById('post-search');
            if (searchInput && searchInput.value.trim().length > 0) {
                params.append('search', searchInput.value.trim());
            }

            // Skip if only 'all' filter is active
            if (!activeFilters.has('all')) {
                // Type filters
                const typeFilters = Array.from(activeFilters).filter(filter =>
                    ['general', 'announcement', 'event', 'financial_report'].includes(filter));

                if (typeFilters.length === 1) {
                    params.append('type', typeFilters[0]);
                }

                // Organization filters
                if (activeFilters.has('personal')) {
                    params.append('organization_filter', 'personal');
                } else if (activeFilters.has('organizations')) {
                    params.append('organization_filter', 'organizations');
                }

                // Additional filters
                if (activeFilters.has('with_images')) {
                    params.append('with_images', 'true');
                }

                if (activeFilters.has('pinned')) {
                    params.append('pinned', 'true');
                }
            }

            // Make AJAX request
            fetch(`/posts-filter?${params.toString()}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update posts container with new HTML
                    postsContainer.innerHTML = data.html;

                    // Update pagination
                    const paginationContainer = document.querySelector('.pagination-container');
                    if (paginationContainer) {
                        if (data.count > 0) {
                            paginationContainer.innerHTML = data.pagination.links;
                            paginationContainer.classList.remove('hidden');
                        } else {
                            paginationContainer.classList.add('hidden');
                        }
                    }

                    // Show/hide empty state
                    const emptyState = document.getElementById('empty-state');
                    if (emptyState) {
                        if (data.count === 0) {
                            // Clone the empty state from the original HTML if needed
                            if (!postsContainer.contains(emptyState)) {
                                postsContainer.appendChild(emptyState.cloneNode(true));
                            }
                            emptyState.classList.remove('hidden');
                        } else {
                            emptyState.classList.add('hidden');
                        }
                    }

                    // Initialize image modal functionality for new posts
                    initializeImageModals();
                } else {
                    console.error('Error fetching filtered posts');
                }

                // Remove loading state
                postsContainer.classList.remove('opacity-50');
            })
            .catch(error => {
                console.error('Error:', error);
                postsContainer.classList.remove('opacity-50');

                // Fallback to client-side filtering if AJAX fails
                clientSideFiltering();
            });
        }

        // Client-side filtering fallback
        function clientSideFiltering() {
            const posts = document.querySelectorAll('.post-card');

            posts.forEach(post => {
                const postType = post.dataset.type;
                const hasOrganization = post.dataset.organization !== '';
                const hasImages = post.dataset.hasImages === 'true';
                const isPinned = post.dataset.isPinned === 'true';

                let shouldShow = activeFilters.has('all');

                // Check if post matches any active filter
                if (!shouldShow) {
                    // Type filters
                    if (activeFilters.has(postType)) {
                        shouldShow = true;
                    }

                    // Organization filters
                    if (activeFilters.has('personal') && !hasOrganization) {
                        shouldShow = true;
                    }

                    if (activeFilters.has('organizations') && hasOrganization) {
                        shouldShow = true;
                    }

                    // Additional filters
                    if (activeFilters.has('with_images') && hasImages) {
                        shouldShow = true;
                    }

                    if (activeFilters.has('pinned') && isPinned) {
                        shouldShow = true;
                    }
                }

                // Show or hide post
                if (shouldShow) {
                    post.classList.remove('hidden');
                } else {
                    post.classList.add('hidden');
                }
            });

            // Show empty state if no posts are visible
            const visiblePosts = document.querySelectorAll('.post-card:not(.hidden)');
            const emptyState = document.getElementById('empty-state');

            if (visiblePosts.length === 0 && emptyState) {
                emptyState.classList.remove('hidden');
                // Hide pagination if no posts are visible
                const pagination = document.querySelector('.pagination-container');
                if (pagination) {
                    pagination.classList.add('hidden');
                }
            } else if (emptyState) {
                emptyState.classList.add('hidden');
                // Show pagination if posts are visible
                const pagination = document.querySelector('.pagination-container');
                if (pagination) {
                    pagination.classList.remove('hidden');
                }
            }
        }

        // Initialize image modal functionality for dynamically loaded posts
        function initializeImageModals() {
            // This function would reinitialize any event handlers for the image modal
            // that might be needed for dynamically loaded content
            console.log('Image modals initialized for new content');
        }

        // Update active filters display
        function updateActiveFiltersDisplay() {
            const activeFiltersContainer = document.getElementById('active-filters');
            const activeFiltersList = document.getElementById('active-filters-list');

            // Clear current filters
            activeFiltersList.innerHTML = '';

            // If only 'all' is active, hide the active filters section
            if (activeFilters.has('all') && activeFilters.size === 1) {
                activeFiltersContainer.classList.add('hidden');
                return;
            }

            // Show active filters section
            activeFiltersContainer.classList.remove('hidden');

            // Create filter badges
            activeFilters.forEach(filter => {
                if (filter === 'all') return; // Skip 'all' filter in the display

                const filterName = getFilterDisplayName(filter);
                const badge = document.createElement('span');
                badge.className = 'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-custom-green text-white';
                badge.innerHTML = `
                    ${filterName}
                    <button type="button" onclick="removeFilter('${filter}')" class="ml-1 inline-flex items-center">
                        <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                `;
                activeFiltersList.appendChild(badge);
            });
        }

        // Get display name for filter
        function getFilterDisplayName(filter) {
            const filterNames = {
                'general': 'General',
                'announcement': '📢 Announcements',
                'event': '📅 Events',
                'financial_report': '💰 Financial Reports',
                'personal': '👤 Personal',
                'organizations': '🏢 Organizations',
                'with_images': '🖼️ With Images',
                'pinned': '📌 Pinned'
            };

            return filterNames[filter] || filter;
        }

        // Remove a specific filter
        function removeFilter(filter) {
            activeFilters.delete(filter);

            // If no filters are active, revert to 'all'
            if (activeFilters.size === 0) {
                activeFilters.add('all');
            }

            updateFilterButtonStates();
            applyFilters();
        }

        // Clear all filters
        function clearAllFilters() {
            activeFilters.clear();
            activeFilters.add('all');
            updateFilterButtonStates();
            applyFilters();
        }
    </script>

    <!-- Filter Styles -->
    <style>
        .filter-btn {
            transition: all 0.2s ease;
        }

        .filter-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .filter-btn.active {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(123, 199, 77, 0.3);
        }

        #posts-container.opacity-50 {
            transition: opacity 0.3s ease;
        }

        /* Loading animation for filter buttons */
        .filter-btn.loading {
            position: relative;
            color: transparent;
        }

        .filter-btn.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* Responsive filter buttons */
        @media (max-width: 768px) {
            .filter-btn {
                font-size: 12px;
                padding: 6px 12px;
            }
        }

        /* Active filter badges */
        .filter-badge {
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.8); }
            to { opacity: 1; transform: scale(1); }
        }
    </style>
</x-feed-layout>
